import { <PERSON><PERSON>, Card, Form, Tabs, Typography, message } from "antd";
import { Download, Upload, Plus } from "lucide-react";
import React, {
  useRef,
  useState,
  useCallback,
  useMemo,
  useEffect,
} from "react";
import DynamictabForm from "./DynamictabForm";
import useStorage from "../../hooks/use-storage";
import { CONSTANTS } from "../../util/constant/CONSTANTS";

const { Title } = Typography;

// Default page structure creator function
const createDefaultPage = (type = "Single Column") => ({
  sitemapLabel: "",
  navigationType: type,
  sections: [],
  columnCount: 0,
  // type.includes("Multi Column") ? 2 : undefined,
});

const DynamicTab = () => {
  const [importing, setImporting] = useState(false);
  const [templatePage, setTemplatePage] = useState(null);
  const [exporting, setExporting] = useState(false);
  const fileInputRef = useRef(null);
  const [activeTab, setActiveTab] = useState("City");
  const formInstancesRef = useRef({});
  const api = useStorage();
  // Initialize pages state with default structure
  const [pages, setPages] = useState(
    {}
    // () => {
    // const initialPages = {
    //   City: createDefaultPage(),
    //   Service: createDefaultPage(),
    //   "Page 2": createDefaultPage(),
    //   "Page 3": {
    //     ...createDefaultPage("Multi Column"),
    //     sections: [
    //       {
    //         sectionLabel: "",
    //         sectionItems: [{ label: "", slug: "" }],
    //       },
    //     ],
    //   },
    // };
    // // form instances are created inside each DynamictabForm (using Form.useForm())
    // // keep ref map for potential external access/cleanup, but don't call hooks here
    // return initialPages;
    // }
  );
  console.log(pages, "pages");

  useEffect(() => {
    // JSON storage calls
    setTimeout(() => {
      api.sendRequest(CONSTANTS.API.templates.get, (res) => {
        console.log("Templates fetched from storage:", res);
        const template = res.find((t) => t.id === "memu53w7j3u1pftta4m");
        //now i wan to template inside pages array type is dynamic the store in setpages
        template?.pages?.forEach((page) => {
          if (page.type === "dynamic") {
            setPages((prev) => ({
              ...prev,
              [page.name]: createDefaultPage(),
            }));
          }
        });
        setTemplatePage(template);
      });
    }, 1000);
  }, [formInstancesRef.current]);
  console.log(pages, "pages", templatePage);
  // Function to get or create a form instance for a tab
  // Return stored form instance if present. Forms are created by DynamictabForm.
  const getFormInstance = useCallback(
    (tabKey) => {
      console.log(tabKey, "tabKey");
      return formInstancesRef.current[tabKey];
    },
    [pages]
  );
  console.log(formInstancesRef, "formInstancesRef");
  // Add new page function
  const addNewPage = useCallback(
    (pageName) => {
      if (!pageName?.trim() || pages[pageName]) {
        message.error("Invalid page name or page already exists");
        return;
      }

      setPages((prev) => ({
        ...prev,
        [pageName]: createDefaultPage(),
      }));
      setActiveTab(pageName);
    },
    [pages]
  );

  // Remove page function
  const removePage = useCallback(
    (pageName) => {
      if (Object.keys(pages).length <= 1) {
        message.error("Cannot remove the last page");
        return;
      }

      setPages((prev) => {
        const newPages = { ...prev };
        delete newPages[pageName];

        // Clean up form instance
        delete formInstancesRef.current[pageName];

        // Update active tab if needed
        if (activeTab === pageName) {
          setActiveTab(Object.keys(newPages)[0]);
        }

        return newPages;
      });
    },
    [pages, activeTab]
  );

  // Import JSON handler
  const handleImportJSON = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // File change handler
  const onFileChange = useCallback(
    async (e) => {
      const file = e.target.files?.[0];
      if (!file) return;

      setImporting(true);
      try {
        const text = await file.text();
        const parsed = JSON.parse(text);

        if (parsed.pages) {
          setPages(parsed.pages);
          // forms will be created by each DynamictabForm when mounted
        }
        if (parsed.activeTab && parsed.pages[parsed.activeTab]) {
          setActiveTab(parsed.activeTab);
        }

        message.success("JSON imported successfully");
      } catch (err) {
        console.error("Import JSON error:", err);
        message.error("Failed to import JSON. Please check the file format.");
      } finally {
        setImporting(false);
        if (fileInputRef.current) fileInputRef.current.value = "";
      }
    },
    [getFormInstance]
  );

  // Export JSON handler
  const handleExportJSON = useCallback(async () => {
    setExporting(true);
    try {
      const exportData = {
        pages,
        activeTab,
        exportDate: new Date().toISOString(),
        version: "1.0.0",
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `dynamic-pages-${Date.now()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success("Data exported successfully");
    } catch (err) {
      console.error("Export error:", err);
      message.error("Failed to export data");
    } finally {
      setExporting(false);
    }
  }, [pages, activeTab]);

  // Update page data
  const handlePageUpdate = useCallback((tab, newData) => {
    setPages((prev) => ({
      ...prev,
      [tab]: {
        ...prev[tab],
        ...newData,
      },
    }));
  }, []);

  // Cleanup effect
  React.useEffect(() => {
    return () => {
      formInstancesRef.current = {};
    };
  }, []);
  
  return (
    <Card>
      <div className="tw-space-y-4">
        <div className="tw-flex tw-justify-between tw-items-center tw-mb-4">
          <Title level={4} className="!tw-mb-0 !tw-text-gray-900">
            Dynamic Page Details
          </Title>
          <div className="tw-space-x-2">
            <Button
              type="primary"
              onClick={() => addNewPage("New-Page")}
              // onClick={handleImportJSON}
              loading={importing}
              className="tw-text-white tw-px-6 tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Import From JSON
            </Button>
            {/* <Button
              type="primary"
              icon={<Download className="tw-w-4 tw-h-4" />}
              onClick={handleExportJSON}
              loading={exporting}
              className="tw-px-6 tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Export JSON
            </Button> */}
            <input
              type="file"
              ref={fileInputRef}
              onChange={onFileChange}
              accept=".json"
              style={{ display: "none" }}
            />
          </div>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {Object.entries(pages).map(([tab, pageData]) => (
            <Tabs.TabPane tab={tab} key={tab}>
              {console.log(tab, "tab", getFormInstance(tab))}
              <DynamictabForm
                tab={tab}
                currentPage={pageData}
                onValuesChange={(_, allValues) =>
                  handlePageUpdate(tab, allValues)
                }
                form={getFormInstance(tab)}
              />
            </Tabs.TabPane>
          ))}
        </Tabs>
      </div>
    </Card>
  );
};

export default DynamicTab;
