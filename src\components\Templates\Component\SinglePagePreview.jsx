import React, { useEffect, useRef, useState } from "react";
import { FileText } from "lucide-react";
import { generateGlobalPreviewHTML } from "../../Components/content";
import { useDragLayer } from "react-dnd";

// Device sizes for responsive preview
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const SinglePagePreview = ({
  templatePage,
  previewMode,
  isDragging,
  contentJSON,
  fileList,
  components = [], // Add components array for repeated component functionality
  dynamicContent = null,
}) => {
  const containerRef = useRef(null);
  const iframeRef = useRef(null);
  const scrollPositionRef = useRef(0);
  const contentJSONRef = useRef(contentJSON);
  const [scale, setScale] = useState(1);
  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } =
    DEVICE_SIZES[previewMode];

  // Iframe scroll preservation effect
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    const handleIframeLoad = () => {
      const iframeDoc =
        iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) return;

      // Save scroll position when user scrolls inside iframe
      const handleIframeScroll = () => {
        scrollPositionRef.current =
          iframeDoc.documentElement.scrollTop || iframeDoc.body.scrollTop;
      };

      // Add scroll listener to iframe content
      iframeDoc.addEventListener("scroll", handleIframeScroll, {
        passive: true,
      });

      // Restore scroll position if content changed
      const contentChanged =
        JSON.stringify(contentJSONRef.current) !== JSON.stringify(contentJSON);
      if (contentChanged) {
        // Restore scroll position after a small delay to allow content to render
        setTimeout(() => {
          if (iframeDoc.documentElement) {
            iframeDoc.documentElement.scrollTop = scrollPositionRef.current;
          }
          if (iframeDoc.body) {
            iframeDoc.body.scrollTop = scrollPositionRef.current;
          }
        }, 100);
        contentJSONRef.current = contentJSON;
      }

      return () => {
        iframeDoc.removeEventListener("scroll", handleIframeScroll);
      };
    };

    // Listen for iframe load
    iframe.addEventListener("load", handleIframeLoad);

    // If iframe is already loaded, call handler immediately
    if (iframe.contentDocument?.readyState === "complete") {
      handleIframeLoad();
    }

    return () => {
      iframe.removeEventListener("load", handleIframeLoad);
    };
  }, [contentJSON]);

  // Scale calculation function (similar to PagePreview)
  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    // Add padding to ensure device doesn't fill entire container
    const availableWidth = bounds.width - (previewMode == "laptop" ? 15 : 15); // 150px padding on each side
    const availableHeight = bounds.height - (previewMode == "laptop" ? 15 : 15); // 150px padding on top/bottom
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;
    requestAnimationFrame(() => {
      setScale(Math.min(widthScale, heightScale, 1)); // Don't scale up beyond 100%
    });
  };

  // Update scale on mount & when device changes
  useEffect(() => {
    recalcScale();
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [previewMode, deviceWidth, deviceHeight]);

  // Template Page Preview Component - Single device frame like PagePreview
  const TemplatePagePreview = ({ templatePage }) => {
    const generatePagePreviewHTML = () => {
      if (!templatePage) return "";

      const firststatic = generateGlobalPreviewHTML({
        type: "page",
        data: templatePage?.components,
        components, // Pass components array for repeated component functionality
        customCSS: templatePage.custom_css,
        customJS: templatePage.custom_js,
        title: templatePage.name || templatePage.name,
        contentJSON,
        fileList,
        // fullData: templatePage?.full_page_content || "",
      });

      if (templatePage.type === "dynamic" && dynamicContent) {
        const replcerofvaluesfromHtml = (html, content) => {
          if (!html) return html;

          let processedHTML = html;
          if (content && typeof content === "object") {
            Object.keys(content).forEach((key) => {
              const value = content[key];
              if (value !== undefined && value !== null) {
                const regex = new RegExp(`\\$\\{${key}\\}`, "g");
                processedHTML = processedHTML.replace(regex, value);
              }
            });
          }
          return processedHTML;
        };
        const slugsAll = [
          {
            id: 1,
            name: "New York",
            slug: "new-york",
          },
          {
            id: 2,
            name: "Los Angeles",
            slug: "los-angeles",
          },
          {
            id: 3,
            name: "Chicago",
            slug: "chicago",
          },
        ];

        const allDiffHtmlPages = slugsAll.map((slugObj) => {
          // "${${slug}_hero_section_title}}",
          const firstRenderSlugReplced = replcerofvaluesfromHtml(firststatic, {
            slug: slugObj.slug,
          });
          // return replcerofvaluesfromHtml(firstRenderSlugReplced, contentJSON);
          // "${chicago_hero_section_title}",
          return firstRenderSlugReplced;
        });

        const allSlugsIdentified = allDiffHtmlPages.map((html, index) => {
          // find all keys from html ${key}
          // only keys which are new genarted by slug rpelcementes
          const slug = slugsAll[index].slug;
          const regex = /\$\{([^}]+)\}/g;
          const regexstartwithdyanmicslug = new RegExp(
            `\\$\\{${slug}_([^}]+)\\}`,
            "g"
          ); // Regex to match ${slug}_* patterns in the string. Replace * with ([^}]+) to match any character except }.
          const matches = html.match(regexstartwithdyanmicslug);
          console.log({ matches }, "matches");

          const keys = matches?.map((match) => match?.replace(/\$\{|\}/g, ""));

          return { html, slug, keys };
        });

        return allSlugsIdentified;
        // return firststatic;

        // return replcerofvaluesfromHtml(firststatic, contentJSON);
      }
      // For static pages or when no dynamic content, use standard generation
      return firststatic;
    };
    const dataValues = generatePagePreviewHTML();

    return (
      <div
        className={`tw-rounded-xl tw-flex tw-justify-center tw-items-center tw-relative `}
        style={{
          height: "auto",
          minHeight:
            previewMode == "laptop"
              ? "320px"
              : previewMode == "tablet"
              ? "490px"
              : "490px",
        }}
      >
        {/* Virtual device frame - exactly like PagePreview */}
        <div
          className="device-frame tw-bg-white tw-rounded-xl  tw-border tw-border-gray-200 tw-absolute"
          style={{
            width: `${deviceWidth}px`,
            height: `${deviceHeight}px`,
            transform: `scale(${scale})`,
            left: "50%",
            top: "50%",
            marginLeft: `-${deviceWidth / 2}px`,
            marginTop: `-${
              deviceHeight /
              (previewMode == "laptop"
                ? 2.4
                : previewMode == "tablet"
                ? 2.4
                : 2.9)
            }px`,
            transition: "all 0.3s ease",
            zIndex: isDragging ? 0 : 25,
          }}
        >
          <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-rounded-xl">
            {templatePage ? (
              // templatePage.type === "dynamic" ? (
              //   dataValues?.map((item, index) => {
              //     return (
              //       <iframe
              //         key={index}
              //         srcDoc={item.html}
              //         className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl tw-relative"
              //         title={`${templatePage.name} Preview`}
              //         style={{
              //           pointerEvents: isDragging ? "none" : "auto",
              //           background: "#fff",
              //         }}
              //       />
              //     );
              //   })
              // ) : (
              <iframe
                ref={iframeRef}
                // srcDoc={templatePage?.full_page_content}
                srcDoc={dataValues}
                className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl tw-relative"
                title={`${templatePage.name} Preview`}
                style={{
                  pointerEvents: isDragging ? "none" : "auto",
                  background: "#fff",
                }}
              />
            ) : (
              // )
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full ">
                <div className="tw-text-center">
                  <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">Page not found</p>
                  <p className="tw-text-sm tw-text-gray-400">
                    This page may have been deleted
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      style={{
        height: "auto",
        minHeight:
          previewMode == "laptop"
            ? `${29}rem`
            : previewMode == "tablet"
            ? `${42}rem`
            : `${43}rem`,
      }}
      className={`tw-w-full  tw-overflow-auto tw-relative tw-rounded-lg `}
    >
      <TemplatePagePreview
        key={templatePage.id}
        templatePage={templatePage}
        // originalPage={originalPage}
      />
    </div>
  );
};

export default SinglePagePreview;
